<?php 
		$time=time();	
		
		$timecheckmodem=$time-10;
													
$sqlsimact="SELECT * from siminfo where status!='1' and time<$timecheckmodem"; 
   $query = $this->db->query($sqlsimact);

					foreach ($query->result() as $rowsma)
					{
    
       $smid=$rowsma->id; 
	   
	   $simbalup = "UPDATE  `siminfo` SET `status` =  '1' WHERE  `siminfo`.`id` ='$smid'"; 
	   $this->db->query($simbalup);

	}
			
           $companydata=$this->mdb->getData('company',array('id'=>1));
		   
		   foreach($companydata as $row) {
			    $system_logo=$row['logo'];
			    $system_title=$row['company_title'];
				$system_name=$row['company_name'];
			   
			   
		   }
          
           
			
			if ($this->session->userdata('admin_session')) {
				
			
				
				$admins = $this->session->userdata('admin_session');
				
				$memberdata=$this->mdb->getData('reseller',array('id'=>$admins->id));
				
				$uname         = $memberdata[0]['username'];
				$uid           = $memberdata[0]['id'];
				$login_allow   = $memberdata[0]['login_allow'];
				
				$member_name   = $memberdata[0]['name'];
				
				$user_cbal     = $memberdata[0]['balance'];
				$user_lmtbal   = $memberdata[0]['balance_limit'];
				$totalsim      = $memberdata[0]['balance']; // Set totalsim for navigation display

				$memberacctype = $memberdata[0]['type'];
				$memberlevel = $memberdata[0]['custype'];
					$query = $this->db->query("select * from reseller where id='$uid'");
foreach ($query->result() as $row)
					{
							$types=$row->type; 
							$uidteype=$row->custype; 
							$edit_oversale=$row->oversale; 
							$myLeft=$row->lft; 
							$myRight=$row->rgt;
							$enbaleotp=$row->enbale_otp;
							
							$remobile=$row->mobile;
							$resemailid=$row->email;
							
							$otponecode=$row->otp; 

							$otppincode=$row->pincode; 
							
					}
					
						$lastlogID = $this->mit->lastlogID($uid, 'admin');
	
	$securid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->secretcode;
	$getdata= $this->session->userdata('securlog');
	$secret = $getdata['secretcode'];
	$otpid = $this->db->get_where('userlog',array('id' =>$lastlogID))->row()->otpcode;
	
	
		if($secret==$securid){
			
			$tokencode = $this->security->get_csrf_hash();
			
				
				$sqlcont="SELECT * FROM device_list WHERE tokenid='$tokencode' and status=1 and userid='$uid'";

					$querycount = $this->db->query($sqlcont);
		
				foreach ($querycount->result() as $row3){
					 $mysafe_status=$row3->status;
					  $myfsttoken=$row3->tokenid;
					   $tblremember=$row3->remember;
					  
					  }

				if($tblremember==0) {
					
					// 2 step verify yes 
				if($enbaleotp==1 && $otponecode!=$otpid) {  
				redirect('adminotp', 'refresh');
				}
				else if ($enbaleotp==0 && $otppincode!=$otpid) { 
				redirect('adminotp', 'refresh');
				} 
					 
				}
				 
           
			
			
			 
		?>
		
		 <?php include "menu_permission.php" ?>
		 
		<?php include "admininc/adminheader.php" ?>

		

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
   
 <div class="mittle"><span class="text"><?php echo $page_title; ?></span></div>
 

 
    <!-- Main content -->
    <section class="content">

    <div class="1table-responsive">
						
     
	
	     <?php if($this->session->flashdata('success')) { ?>
	 <div class="mypage">
					    <div class="alert alert-success fade in">
                                                    <strong><?php echo $this->session->flashdata('success'); 
													
													///$this->session->unset_flashdata('success');
													
													?></strong> 
                                                </div>
												</div>
												
												<?php }
												 else if($this->session->flashdata('error')) { ?>
												 <div class="mypage">
					    <div class="alert alert-danger fade in">
                                                    <strong><?php echo $this->session->flashdata('error'); 
													//$this->session->unset_flashdata('error');
													?></strong> 
                                                </div>
												 </div>
												
												<?php } ?>
												
											
<?php												


		
// password reset for redirect


		if($login_allow == 3 ) {
			
		$this->session->set_flashdata('error', 'Please Your Submit Mobile Number');
		
		redirect('admin/newpass', 'refresh');	
		}
		
		if($login_allow == 4 ) {
			
		$this->session->set_flashdata('error', 'Please Your Submit Mobile Number');
		
		redirect('admin/newpin', 'refresh');	
		}
		
include $page_name.'.php';
?> 


  </div>

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->


<?php include "admininc/adminfooter.php";  }else { redirect('admin/logout', 'refresh');} //last login
			
			} // sessionid ?>


			