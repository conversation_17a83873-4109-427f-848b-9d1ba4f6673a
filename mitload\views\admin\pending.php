	<div class="mypage">
			<div class="top10">&nbsp;</div>
			 <?php echo form_open('' , array('id' => '' , 'class' => 'form-inline filter'));?>
			<div class="form-group" style="margin-right:20px;">
			<label for="status">Show</label><br/>
				<select class="form-control input-xs" name="limit" id="limit">
		
				
				
				<option value="50"  <?php if($limit=="50") { echo "selected='selected'";}?> >50</option>
				<option value="100"  <?php if($limit=="100") { echo "selected='selected'";}?>>100</option>
				<option value="300" <?php if($limit=="300") { echo "selected='selected'";}?>>300</option>
				<option value="500" <?php if($limit=="500") { echo "selected='selected'";}?>>500</option>
				<option value="1000" <?php if($limit=="1000") { echo "selected='selected'";}?>>1000</option>
				<option value="2000" <?php if($limit=="2000") { echo "selected='selected'";}?>>2000</option>
				
				
				
				</select>
			</div>



			<div class="form-group">
			<label for="number">Number</label><br/>
			<input type="text" class="form-control input-xs" name="number" id="number" placeholder="Number" size="18" value="<?php echo $number;?>">
			</div>
			<!-- reseller list-->
			<div class="form-group" style="margin-right:70px;">
			<?php include('reseler_level_list.php'); ?>
			
								
			</div>
		
			<div class="form-group">
			<label for="service">Services</label><br/>
				<select class="form-control input-xs" name="op" id="service">
					<option value="all" <?php if($op=="all") { echo "selected='selected'";}?>>--Any--</option> 
				
							<?php
								
		
		$query = $this->db->query("SELECT * from module where status='1' and id>=0 and 6<=id order by sorder asc");
foreach ($query->result() as $row)
					{
						
    
		$portid=$row->id; 
		$title_op=$row->title; 
        $serviceid=$row->serviceid; 

	

?>												
<option value="<?php echo $serviceid ?>" <?php if($op=="$serviceid") { echo "selected='selected'";}?> ><?php echo $title_op ?></option> 	
<?php } ?>

</select>
			</div>
			
			<div class="form-group">
			<label for="status">Status</label><br/>
				<select class="form-control input-xs" name="status" id="status">
			<option value="all" <?php if($status=="all") { echo "selected='selected'";}?>>--Any--</option>
				<option value="0"  <?php if($status=="0") { echo "selected='selected'";}?> >Pending</option>
				<option value="5" <?php if($status=="5") { echo "selected='selected'";}?>>Waiting</option>
				<option value="4" <?php if($status=="4") { echo "selected='selected'";}?>>Processing</option>
				<option value="2" <?php if($status=="2") { echo "selected='selected'";}?>>Fail</option>
				
				
				</select>
			</div>



			<?php 
			$from1 = $from1;
			$to1 = $to1;

			?>
			
			<div class="form-group">
			<label for="date1">Date From </label><br/>
			<input type="text" class="form-control input-xs" name="from1" id="date1" placeholder="Date From" size="18" value="<?php if(empty($from1)) { echo $date = date('Y-m-d', mktime(0, 0, 0, date('m'), date('d') - 30, date('Y'))); }else { echo $from1; }?>">
			</div>
			<div class="form-group">
			<label for="date2">Date To</label><br/>
			<input type="text" class="form-control input-xs" name="to1" id="date2" placeholder="Date To" size="18" value="<?php if(empty($to1)) { echo $date = date('Y-m-d'); }else { echo $to1; }?>">
			</div>
			
			
			<div class="form-group">
			<label class="" for="">&nbsp;</label><br/>
			
			<button type="submit" class="btn btn-primary btn-xs"><span class="glyphicon glyphicon-search"></span> Filter</button>
			
			<a href="javascript:void(0)" class="btn btn-danger btn-xs" onClick="window.print();return false"><span class="glyphicon glyphicon-print"></span> Print</a>
			
			</div>
				<div class="top10">&nbsp;</div>

				</form>
				
				 <?php echo form_open('' , array('name' => 'dataform' , 'role' => 'form'));?>
		   
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
				  <th width="25"><input type="checkbox" name="checkAll" onclick="checkallbox(this);"></th>
				   <th><?= translate('sl'); ?></th>
                    <th><?= translate('number'); ?></th>
                    <th><?= translate('amount'); ?></th>
                    <th><?= translate('cost'); ?></th>
                     <th><?= translate('type'); ?></th>
                     <th><?= translate('response'); ?></th>
                      <th><?= translate('service'); ?></th>
                       <th><?= translate('sender'); ?></th>
                        <th><?= translate('balance'); ?></th>
                         <th><?= translate('date'); ?></th>
                         <th><?= translate('status'); ?></th>
						 <th><?= translate('route'); ?></th>
                         <th><?= translate('trxid'); ?></th>
                         <th><?= translate('Operator'); ?></th>
                     <th class="text-nowrap"><?= translate('action'); ?></th>
                  </tr>
                </thead>
                <tbody>
                
                <?php
                // Initialize totals
                $cbalan = 0;
                $tcost = 0;

                	foreach ($pending_history as $row)
					{
					   
                       $service = $row->service;
                       $type = $row->type;
                       $member=$row->userid;
						$routes=$row->route; 
						$route_id=$row->route_id; 
                       // $member=$row->userid;
                       
                       $flxstatus=$row->status;
                       $flxlocal=$row->local;
                       if($flxstatus!=3) {
                       $onlysucess_sid=$row->id; 
                       }else {
                         $flxid=$row->id; 
                       }
                   
                        $servicenick = $this->db->get_where('response',array('id_order' =>$row->id))->row()->body;
                            $op = $this->db->get_where('response',array('id_order' =>$row->id))->row()->body;
                 if($op!=NULL)$er='✔';
    $part = explode("*",$servicenick);
    $option= $part[0];
                        
                        $servicename = $this->db->get_where('module',array('serviceid' =>$service))->row()->title;
                        
                        $membername = $this->db->get_where('reseller',array('id' =>$member))->row()->username;
                       
                       
                       // $lastbal = $this->db->get_where('reseller',array('id' =>$member))->row()->balance;
                       
                       if($service=='64' or $service=='16' or $service=='512' or $service=='16384') { 

if($type==1) {$ot="Prepaid";} else {$ot="PostPaid";} 

}else { 
  if($type==1) {$ot="Personal";} else {$ot="Agent";} 
  if(empty($type) or ($type=='none') ) {$ot="None";} 
  }
  
if($flxstatus==0 && $flxlocal==0){$msg="<font color=#DE00FF><b>Pending</b></font>";} 
if($flxstatus==1){$msg="<font color=#60C36A><b>Success</b></font></a>";} 
if($flxstatus==3){$msg="<font color=#FF0000><b>Cancelled</b></font>";} 
if($flxstatus==4){$msg="<font color=blue><b>Processing</b></font></a>";} 
if($flxstatus==5){$msg="<font color=red><b>Waiting</b></font></a>";} 
if($flxstatus==2){$msg="<font color=black><b>Faild</b></font></a>";} 



    if($flxstatus!=3) { 			
$query2 = $this->db->query("SELECT * from trans where send_sl='$onlysucess_sid' and userid='$member' and status!=3");
}else {
  $query2 = $this->db->query("SELECT * from trans where send_sl='$flxid' and userid='$member' and status!=3");  
   
}
$rowtrans = $query2->row();


$accountbalance=$rowtrans->accountbalance; 
$oldbal=$rowtrans->oldbal; 


 $costamount=$rowtrans->debit; 
	
	if($costamount>0) {
		$req_costamount=$rowtrans->debit; 
   
	}else {
		
		$req_costamount=$row->cost; 
	}
	 $tcost+=$req_costamount; 

 $cbalan+=$row->balance; 
 
  if($routes=="modem" or $routes=="manual") {
	  $routename = $row->send_number;
  }else {
 
  $routename = $this->db->get_where('routing',array('id' =>$route_id))->row()->title;
  }
           
  
  
					   ?>
                  <tr>
				  	<td><input type="checkbox" name="id[]" value="<?php echo $row->id; ?>" ></td>
				  <td><?php echo $row->id;  ?></td>
                    <td><?php echo $row->phone; echo " ($option-$er)"; ?></td>
                    <td><?php echo $row->balance;  ?><br/> <?php if($service=='64'){ echo $this->db->get_where('drive_package',array('id' =>$row->send_number))->row()->pk_name;} else{
                    echo $this->db->get_where('net_package',array('id' =>$row->send_number))->row()->pk_name;
                    } ?> </td>
                    <td><?php echo $req_costamount;  ?></td>
                    
                    <td><?php echo $ot;  ?></td>
                     <td><?php echo $row->apiresponse;  ?></td>
                    <td><?php echo $servicename;  ?></td>
                    <td><a href='javascript:void(0)' onClick="javascript:PopUp('<?= base_url() ?>/admin/otherservice/user_view/<?php echo $this->mdb->passwordChanger('encrypt', $member); ?>', 500, 403); return false;"><?php echo $membername;  ?></a></td>
                    <td><?php echo $oldbal;  ?> - <?php echo $accountbalance;  ?></td>
                    
                    <td><?php echo $row->send_time_stamp;  ?></td>
                    <td><?php echo $msg;  ?></td>
					<td><?php echo $routename;  ?></td>
                    <td><?php echo $row->trxid;  ?></td>
                    <td><?php echo $row->pcode;  ?></td>
                    <td class="text-nowrap">

                    <?php if($flxstatus==0 || $flxstatus==4 || $flxstatus==5 || $flxstatus==2){ ?>
						
						<!--<a href='javascript:void(0)' onclick="javascript:PopUp('partial_pay.php?id=<?php echo $row->id; ?>', 390, 250); return false;" title="Partial Success" class="btn btn btn-xs"> Partial</a>-->
						<?php if($page_request_confirm==1) { ?>
						<a href='javascript:void(0)' onclick="javascript:PopUp('<?= base_url() ?>admin/RechargeStatus/success/<?php echo $this->mdb->passwordChanger('encrypt', $row->id); ?>', 490, 450); return false;" title="Confirm" class="btn btn-success btn-xs"><span class="glyphicon glyphicon-ok"></span></a>
						
						<?php } if($page_request_resend==1) { ?>
						<?php if($flxstatus!=1) {?> 
						<a href='javascript:void(0)' onclick="javascript:PopUp('<?= base_url() ?>admin/RechargeStatus/resend/<?php echo $this->mdb->passwordChanger('encrypt', $row->id); ?>', 490, 400); return false;" title="Resend" class="btn btn-primary btn-xs"><span class="glyphicon glyphicon-repeat"></span></a><?php } } ?>
						
							<?php  if($page_request_cancel==1) { ?>

						<a href='javascript:void(0)' onclick="javascript:PopUp('<?= base_url() ?>admin/RechargeStatus/cancel/<?php echo $this->mdb->passwordChanger('encrypt', $row->id); ?>', 450, 400); return false;" title="Cancel & Refund" class="btn btn-danger btn-xs"><span class="glyphicon glyphicon-remove"></span></a> 
						
							<?php } ?>
							
							<?php  if($page_request_details==1) { ?>

						<a href='javascript:void(0)' onclick="javascript:PopUp('<?= base_url() ?>admin/RechargeStatus/userShow/<?php echo $this->mdb->passwordChanger('encrypt', $row->id); ?>', 680, 610); return false;" title="View Details" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-info-sign"></span></a>
					<?php } } ?>

                    </td>
                  </tr>
                  
                  <?php } ?>
                 
               
              

                </tbody>

               
				<tfoot>
					<tr>
					<th colspan="2">Total</th>
					<th><?php echo ($cbalan); ?></th>
					<th><?php echo ($tcost); ?></th>
					<th colspan="10">&nbsp;</th>
					
					</tr>
				</tfoot>

              </table>
			  
			    <div class="pagination" style="float: right;">
                                <?php echo $links; ?> 
                                </div> 
								
								
				<br>
				
				
				
				<div class="col-md-12 fleft">
				<label for="action" style="color:#666">Bulk Action</label>
				<div class="form-group" style="width:150px;">
					<select name="action" id="action" class="form-control input-xs" onChange="pendingSubmit();">
					<option value="">--Select--</option>
					<?php if($page_request_resend==1) { ?>
					<option value="resend">Resend</option>
					<?php } if($page_request_confirm==1) { ?>
					<option value="waitting">Waiting</option>
					<?php } if($page_request_confirm==1) { ?>
					<option value="confirm">Manual Complete</option>
					<?php } if($page_request_cancel==1) { ?>
					<option value="process">Process</option>
				
					<?php } if($page_request_cancel==1) { ?>
					<option value="cancel">Cancel</option>
					<?php } ?>
					</select>
				  </div>
				  
				  <div class="form-group" style="width:150px;">
				  <input type="text" id="canclmsg" name="canclmsg" />
				  </diV>
				  
				</div> 
				
				<div class="col-md-12 fleft">
				<div class="summery">
					<p>Total Summary</p>
					<table>
										<tr><td><strong>Total</strong>: </td><td class="amt"><?php echo ($cbalan); ?></td></tr>
					</table>
				</div>
			</div> 
		
            </div>

            </form>
        
      </div>
      <!-- /.row -->
<?php
	
$page = $_SERVER['PHP_SELF'];
 $sec = "100";
 header("Refresh: $sec;");
 ?>