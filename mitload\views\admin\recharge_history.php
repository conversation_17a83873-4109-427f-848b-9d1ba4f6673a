	<div class="mypage">
			<div class="top10">&nbsp;</div>
			 <?php echo form_open('' , array('id' => '' , 'class' => 'form-inline filter'));?>
			<div class="form-group" style="margin-right:20px;">
			<label for="status">Show</label><br/>
				<select class="form-control input-xs" name="limit" id="myreseller">
		
		
				<option value="50"  <?php if($limit=="50") { echo "selected='selected'";}?> >50</option>
				<option value="100"  <?php if($limit=="100") { echo "selected='selected'";}?>>100</option>
				<option value="300" <?php if($limit=="300") { echo "selected='selected'";}?>>300</option>
				<option value="500" <?php if($limit=="500") { echo "selected='selected'";}?>>500</option>
				<option value="1000" <?php if($limit=="1000") { echo "selected='selected'";}?>>1000</option>
				
				</select>
			</div>



		

			<div class="form-group">
			<label for="service">Services</label><br/>
				<select class="form-control input-xs" name="op" id="service">
					<option value="all" <?php if($op=="all") { echo "selected='selected'";}?>>--Any--</option> 
				
							<?php
								
		
		$query = $this->db->query("SELECT * from module where status='1' order by sorder asc");
foreach ($query->result() as $row)
					{
						
    
		$portid=$row->id; 
		$title_op=$row->title; 
        $serviceid=$row->serviceid; 

	

?>												
<option value="<?php echo $serviceid ?>" <?php if($op=="$serviceid") { echo "selected='selected'";}?> ><?php echo $title_op ?></option> 	
<?php } ?>

</select>
			</div>
			

			
			
			<div class="form-group">
			<label for="operator">Operator</label><br/>
				<select class="form-control input-xs" name="operator" id="operator">
				<option value="all" <?php if($operator=="all") { echo "selected='selected'";}?> >--View All--</option>
				<option value="gp"  <?php if($operator=="gp") { echo "selected='selected'";}?> >GrameenPhone</option>
				<option value="robi" <?php if($operator=="robi") { echo "selected='selected'";}?> >Robi</option>
				<option value="blink" <?php if($operator=="blink") { echo "selected='selected'";}?> >Banglalink</option>
				<option value="airtel" <?php if($operator=="airtel") { echo "selected='selected'";}?> >Airtel</option>
				<option value="teletalk" <?php if($operator=="teletalk") { echo "selected='selected'";}?> >TeleTalk</option>
				

				</select>
			</div>


			



			<?php 
			$from1 = $from1;
			$to1 = $to1;

			?>
			
			<div class="form-group">
			<label for="date1">Date From </label><br/>
			<input type="text" class="form-control input-xs" name="from1" id="date1" placeholder="Date From" size="18" value="<?php if(empty($from1)) { echo $date = date('Y-m-d', mktime(0, 0, 0, date('m'), date('d') - 30, date('Y'))); }else { echo $from1; }?>">
			</div>
			<div class="form-group">
			<label for="date2">Date To</label><br/>
			<input type="text" class="form-control input-xs" name="to1" id="date2" placeholder="Date To" size="18" value="<?php if(empty($to1)) { echo $date = date('Y-m-d'); }else { echo $to1; }?>">
			</div>
			
			
			<div class="form-group">
			<label class="" for="">&nbsp;</label><br/>
			
			<button type="submit" class="btn btn-danger btn-xs"><span class="glyphicon glyphicon-search"></span> Filter</button>
			</div>
				<div class="top10">&nbsp;</div>

		   
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                  <th width=30"><input type="checkbox" name="checkAll" onclick="checkallbox(this);"></th>
				  	<th >Date</a></th>
												<th >Transaction ID</a></th>
												<th >Service</a></th>
												<th >Operator</a></th>
												<th >SIM No</a></th>
												<th >Sender</a></th>
												<th >Old Balance</a></th>
												<th >Recharge Amount</a></th>
												<th >Balance</a></th>
                  </tr>
                </thead>
                <tbody>
                
                <?php
                // Initialize totals
                $tcost = 0;
                $cbalan = 0;

                	foreach ($recharge_list as $row)
					{
					   
                       $service = $row->service;
                        $type = $row->type;
                        $member=$row->userid;
                       // $member=$row->userid;
                       
                       $flxstatus=$row->status;
                       $flxlocal=$row->local;
                       if($flxstatus!=3) {
                       $onlysucess_sid=$row->id; 
                       }else {
                         $flxid=$row->id; 
                       }
                   
                       
                        
                        $servicename = $this->db->get_where('module',array('serviceid' =>$service))->row()->title;
                        
                        $membername = $this->db->get_where('reseller',array('id' =>$member))->row()->username;
                       
                       
                       // $lastbal = $this->db->get_where('reseller',array('id' =>$member))->row()->balance;
                       
                       if($service=='64' or $service=='16' or $service=='512' or $service=='16384') { 

if($type==1) {$ot="Prepaid";} else {$ot="PostPaid";} 

}else { 
  if($type==1) {$ot="Personal";} else {$ot="Agent";} 
  if(empty($type) or ($type=='none') ) {$ot="None";} 
  }
 

 $cbalan+=$row->lastbal; 
        $tcost+=$row->credit; 
  
  
					   ?>
                  <tr>
                  <td><input type="checkbox" name="chkbox[]" value="<?php echo $row->id;  ?>" ></td>
				 
				
					<td><?php echo $row->datetime; ?></td>
						<td><?php echo $row->trxid; ?></td>
						<td><?php echo $servicename; ?></td>
					<td><?php echo $row->telco; ?></td>
						
					<td><?php echo $row->sim; ?></td>
					<td><?php echo $row->sender; ?></td>
					<td><?php echo $row->oldbal; ?></td>
						<td><?php echo $row->credit; ?></td>
					<td><?php echo $row->lastbal; ?></td>
					
                   
                  </tr>
                  
                  <?php } ?>
                 
               
                </tbody>

               
				<tfoot>
					<tr>
					<th colspan="7">Total</th>
					<th><?php echo ($tcost); ?></th>
					<th><?php echo ($cbalan); ?></th>
					<th colspan="7"></th>
					</tr>
				</tfoot>

              </table>


              Showing rows <?= $start ?> - <?= $start+$limit?> (<?= $total_pagescount ?> total Page =  <?= $lastpage = ceil($total_pagescount/$limit);  ?>)

             
			  
			    <div class="pagination" style="float: right;">
                                <?php echo $links; ?> 
                                </div> 
            </div>

            </form>
        
      </div>
      <!-- /.row -->
<?php
	
$page = $_SERVER['PHP_SELF'];
 $sec = "100";
 header("Refresh: $sec;");
 ?>