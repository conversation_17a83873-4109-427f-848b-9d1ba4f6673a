	<div class="mypage">
			<div class="top10">&nbsp;</div>
			 <?php echo form_open('' , array('id' => '' , 'class' => 'form-inline filter'));?>
			<div class="form-group" style="margin-right:20px;">
			<label for="status">Show</label><br/>
				<select class="form-control input-xs" name="limit" id="myreseller">
		

		
				<option value="50"  <?php if($limit=="50") { echo "selected='selected'";}?> >50</option>
				<option value="100"  <?php if($limit=="100") { echo "selected='selected'";}?>>100</option>
				<option value="300" <?php if($limit=="300") { echo "selected='selected'";}?>>300</option>
				<option value="500" <?php if($limit=="500") { echo "selected='selected'";}?>>500</option>
				<option value="1000" <?php if($limit=="1000") { echo "selected='selected'";}?>>1000</option>
				
				</select>
			</div>



			<div class="form-group">
			<label for="number">Number</label><br/>
			<input type="text" class="form-control input-xs" name="number" id="number" placeholder="Number" size="18" value="<?php echo $number;?>">
			</div>
			<!-- reseller list-->
			<div class="form-group" style="margin-right:70px;">
			<?php include('reseler_level_list.php'); ?>
			
								
			</div>
		
			<div class="form-group">
			<label for="service">Services</label><br/>
				<select class="form-control input-xs" name="op" id="service">
					<option value="all" <?php if($op=="all") { echo "selected='selected'";}?>>--Any--</option> 
				
							<?php
								
		
		$query = $this->db->query("SELECT * from module where status='1' and id>=0 and 6<=id order by sorder asc");
foreach ($query->result() as $row)
					{
						
    
		$portid=$row->id; 
		$title_op=$row->title; 
        $serviceid=$row->serviceid; 

	

?>												
<option value="<?php echo $serviceid ?>" <?php if($op=="$serviceid") { echo "selected='selected'";}?> ><?php echo $title_op ?></option> 	
<?php } ?>

</select>
			</div>
			
			<div class="form-group">
			<label for="status">Status</label><br/>
				<select class="form-control input-xs" name="status" id="status">
			<option value="all" <?php if($status=="all") { echo "selected='selected'";}?>>--Any--</option>
				<option value="0"  <?php if($status=="0") { echo "selected='selected'";}?> >Pending</option>
				<option value="1"  <?php if($status=="1") { echo "selected='selected'";}?>>Successful</option>
				<option value="2" <?php if($status=="2") { echo "selected='selected'";}?>>Failed</option>
				<option value="3" <?php if($status=="3") { echo "selected='selected'";}?>>Canceled</option>
				<option value="5" <?php if($status=="5") { echo "selected='selected'";}?>>Waiting</option>
				<option value="4" <?php if($status=="4") { echo "selected='selected'";}?>>Processing</option>
				
				
				</select>
			</div>



			<?php 
			$from1 = $from1;
			$to1 = $to1;

			?>
			
			<div class="form-group">
			<label for="date1">Date From </label><br/>
			<input type="text" class="form-control input-xs" name="from1" id="date1" placeholder="Date From" size="18" value="<?php if(empty($from1)) { echo $date = date('Y-m-d', mktime(0, 0, 0, date('m'), date('d') - 0, date('Y'))); }else { echo $from1; }?>">
			</div>
			<div class="form-group">
			<label for="date2">Date To</label><br/>
			<input type="text" class="form-control input-xs" name="to1" id="date2" placeholder="Date To" size="18" value="<?php if(empty($to1)) { echo $date = date('Y-m-d'); }else { echo $to1; }?>">
			</div>
			
			
			<div class="form-group">
			<label class="" for="">&nbsp;</label><br/>
			
			<button type="submit" class="btn btn-primary btn-xs"><span class="glyphicon glyphicon-search"></span> Filter</button>
			
			<a href="javascript:void(0)" class="btn btn-danger btn-xs" onClick="window.print();return false"><span class="glyphicon glyphicon-print"></span> Print</a>
			
			</div>
				<div class="top10">&nbsp;</div>

		   
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
				   <th><?= translate('sl'); ?></th>
                    <th><?= translate('number'); ?></th>
                    <th><?= translate('amount'); ?></th>
                   
                    <th><?= translate('cost'); ?></th>
                     <th><?= translate('type'); ?></th>
                      <th><?= translate('service'); ?></th>
                       <th><?= translate('sender'); ?></th>
                        <th><?= translate('balance'); ?></th>
                         <th><?= translate('date'); ?></th>
                         <th><?= translate('status'); ?></th>
                         <th><?= translate('trxid'); ?></th>
						  <th><?= translate('sim_balance'); ?></th>
						   <th><?= translate('route'); ?></th>
						   
                    <th class="text-nowrap"><?= translate('action'); ?></th>
                  </tr>
                </thead>
                <tbody>
                
                <?php
                // Initialize totals
                $tcost = 0;
                $cbalan = 0;
                $er = '';

                	foreach ($recharge_history as $row)
					{
					   
                       $service = $row->service;
                        $type = $row->type;
                        $member=$row->userid;
                        $routes=$row->route; 
						$route_id=$row->route_id; 
                       
                       $flxstatus=$row->status;
                       $flxlocal=$row->local;
                       if($flxstatus!=3) {
                       $onlysucess_sid=$row->id; 
                       }else {
                         $flxid=$row->id; 
                       }
                   
                       
                           $response_row = $this->db->get_where('response',array('id_order' =>$row->id))->row();
                           $servicenick = $response_row ? $response_row->body : '';
                            $op = $response_row ? $response_row->body : '';
                 if($op!=NULL)$er='✔'; else $er='';
    $part = $servicenick ? explode("*",$servicenick) : array('');
    $option= $part[0];
                     $module_row = $this->db->get_where('module',array('serviceid' =>$service))->row();
                     $servicename = $module_row ? $module_row->title : 'Unknown';
                        
                     $membername = $this->db->get_where('reseller',array('id' =>$member))->row()->username;
                       
                       
                       // $lastbal = $this->db->get_where('reseller',array('id' =>$member))->row()->balance;
                       
                       if($service=='64' or $service=='16' or $service=='512' or $service=='16384') { 

if($type==1) {$ot="Prepaid";} else {$ot="PostPaid";} 

}else { 
  if($type==1) {$ot="Personal";} else {$ot="Agent";} 
  if(empty($type) or ($type=='none') ) {$ot="None";} 
  }
  
if($flxstatus==0 && $flxlocal==0){$msg="<font color=#DE00FF><b>Pending</b></font>";} 
if($flxstatus==1){$msg="<font color=#60C36A><b>Success</b></font></a>";} 
if($flxstatus==3){$msg="<font color=#FF0000><b>Cancelled</b></font>";} 
if($flxstatus==4){$msg="<font color=blue><b>Processing</b></font></a>";} 
if($flxstatus==5){$msg="<font color=red><b>Waiting</b></font></a>";} 
if($flxstatus==2){$msg="<font color=black><b>Faild</b></font></a>";} 



    if($flxstatus!=3) { 			
$query2 = $this->db->query("SELECT * from trans where send_sl='$onlysucess_sid' and userid='$member' and status!=3");
}else {
 $query2 = $this->db->query("SELECT * from trans where send_sl='$flxid' and userid='$member' and status!=3");  
   
}
$rowtrans = $query2->row();


$accountbalance=$rowtrans->accountbalance; 
$oldbal=$rowtrans->oldbal; 


 $costamount=$rowtrans->debit; 
	
	if($costamount>0) {
		$req_costamount=$rowtrans->debit; 
   
	}else {
		
		$req_costamount=$row->cost; 
	}
	 if($flxstatus==1) { 
	 $tcost+=$req_costamount; 

 $cbalan+=$row->balance; 
	 }
           
   if($routes=="modem" or $routes=="manual") {
	  $routename = $row->send_number;
  }else {
 
  $routename = $this->db->get_where('routing',array('id' =>$route_id))->row()->title;
  }
           
  
					   ?>
                  <tr>
				  <td><?php echo $row->id;  ?></td>
                    <td><?php echo $row->phone; echo " ($row->pcode | $option-$er)"; ?></td>
                    <td><?php echo $row->balance;  ?></td>
                    
                    <td><?php echo $req_costamount;  ?></td>
                    
                    <td><?php echo $ot;  ?></td>
                    <td><?php echo $servicename;  ?></td>
                    <td><a href='javascript:void(0)' onClick="javascript:PopUp('<?= base_url() ?>/admin/otherservice/user_view/<?php echo $this->mdb->passwordChanger('encrypt', $member); ?>', 500, 403); return false;"><?php echo $membername;  ?></a></td>
                    <td><?php echo $oldbal;  ?> - <?php echo $accountbalance;  ?></td>
                    
                    <td><?php echo $row->send_time_stamp;  ?></td>
                    <td><?php echo $msg;  ?></td>
                    <td><?php echo $row->trxid;  ?></td>
					<td><?php echo $row->simBalance;  ?></td>
					<td><?php echo $row->send_number;  ?></td>
                   
                    <td class="text-nowrap">


					<a href='javascript:void(0)' onclick="javascript:PopUp('<?= base_url() ?>admin/RechargeStatus/userShow/<?php echo $this->mdb->passwordChanger('encrypt', $row->id); ?>', 680, 610); return false;" title="View Details" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-info-sign"></span></a>
						
                    </td>
                  </tr>
                  
                  <?php } ?>
                 
               
                </tbody>

               
				<tfoot>
					<tr>
					<th colspan="2">Total</th>
					<th><?php echo number_format($cbalan,2); ?></th>
					<th><?php echo number_format($tcost,2); ?></th>
					<th colspan="10">&nbsp;</th>
					
					</tr>
				</tfoot>

              </table>
			  
			    <div class="pagination" style="float: right;">
                                <?php echo $links; ?> 
                                </div> 
            </div>

            </form>
        
      </div>
      <!-- /.row -->
