<div class="mypage">
			

			<div class="top10">&nbsp;</div>

		  <?php echo form_open('' , array('id' => '' , 'class' => 'form-inline filter'));?>
	
				<div class="form-group" style="margin-right:20px;">
			<label for="status">Show</label><br/>
				<select class="form-control input-xs" name="limit" id="myreseller">
		<option value="10"  <?php if($limit=="10") { echo "selected='selected'";}?> >10</option>
				<option value="50"  <?php if($limit=="50") { echo "selected='selected'";}?> >50</option>
				<option value="100"  <?php if($limit=="100") { echo "selected='selected'";}?>>100</option>
				<option value="300" <?php if($limit=="300") { echo "selected='selected'";}?>>300</option>
				<option value="500" <?php if($limit=="500") { echo "selected='selected'";}?>>500</option>
				<option value="1000" <?php if($limit=="1000") { echo "selected='selected'";}?>>1000</option>
				
				</select>
			</div>

		
			<!-- reseller list-->
			<div class="form-group" style="margin-right:70px;">
			<?php include('reseler_level_list.php'); ?>
			
								
			</div>
			
		
						
			<div class="form-group">
			<label class="" for="exampleInputPassword2">&nbsp;</label><br/>
			
			<button type="submit" class="btn btn-danger btn-xs"><span class="glyphicon glyphicon-search"></span> Filter</button>
			<input type="hidden" class="btn btn-primary" name="todo" value="Search">
			</div>
			</form>			<div class="top10">&nbsp;</div>

			
		
		 <?php echo form_open('' , array('name' => 'dataform' , 'role' => 'form'));?>
		 <div class="table-responsive">
		<table class="table table-striped table-hover">
				<thead>
					<tr>
						<th><input type="checkbox" name="checkAll" onclick="checkallbox(this);"></th>

                   <th>Nr.</th>
						<th>Time</th>
						
						<th>IP</th>
						<th>User</th>
						
						<th>Browser + OS</th>
						
						<th >2 Step Verify</th>
						<th >DeviceStatus</th>
						<th >Action</th>
                  </tr>
                </thead>
                <tbody>
				<?php
				foreach ($device_list as $row)
					{
						 $devsts=$row->status; 
						 $p_id=$row->p_id;
                         
                         $m_id=$row->id;



$id=$row->id; 
$ucid=$row->userid; 
$log_time=$row->date; 
$browser=$row->browser;
$platform = isset($row->platform) ? $row->platform : 'Unknown';
$logout_time = isset($row->logout_time) ? $row->logout_time : 'N/A';
$ip=$row->ip; 


$country=$row->country; 

$device_id=$row->tokenid; 

$remember=$row->remember; 

$customer = $this->db->get_where('reseller',array('id' =>$ucid))->row()->username;	
						 
if($p_id==0) {$parent="Admin";} else { 
$sql3="SELECT * FROM `reseller` WHERE id='$p_id'"; 

$query = $this->db->query($sql3);

					foreach ($query->result() as $row3)
					{
							$parent=$row3->username;
					}
							

} 
 
							?>
                  <tr>
				  <td><input type="checkbox" name="id[]" value="<?php echo $row->id;  ?>" ></td>

                 
					<td><?php echo $id; ?></a></td>
					<td><?php echo $log_time; ?></td>
					
					<td><?php echo $ip; ?><a href="http://www.ip-adress.com/whois/<?php echo $ip; ?>" target="_blank" style="font-weight:normal">WhoIs</a></td>
					<td><a href='javascript:void(0)' onClick="javascript:PopUp('user_view.php?id=<?php echo $ucid; ?>', 500, 403); return false;"><?php echo $customer; ?></a></td>
					
					<td><?php echo $browser; ?></td>
					
					
					<td> 
					<?php if($remember==1) { ?>
						Yes
						<?php } else { ?>
						No
						<?php } ?>
						</td>
						<td>
						<?php if($devsts==0) { ?>
						<font color="#FF0000">Deactive</font>
						<?php } else { ?>
						<font color="green">Active </font>
						<?php } ?>
						</td>
					<td> 
					
					
						
					<?php if($devsts==0) { ?>
						<a href='<?= base_url() ?>admin/device_action/allow/<?php echo $this->mdb->passwordChanger('encrypt', $id); ?>' title="Modify" class="btn btn-primary btn-xs">Allow</a>
						<?php } else { ?>
						<a href='<?= base_url() ?>admin/device_action/disable/<?php echo $this->mdb->passwordChanger('encrypt', $id); ?>' title="Delete" class="btn btn-danger btn-xs">Deny</a>
						<?php } ?>
						<a href='<?= base_url() ?>admin/device_action/delete/<?php echo $this->mdb->passwordChanger('encrypt', $id); ?>' title="Delete" class="btn btn-danger btn-xs"><span class="glyphicon glyphicon-trash"></span></a>
					
					
					 </td>
					
				</tr>
                 
					<?php } ?>
				 
                </tbody>
              </table>
</div>
			  <br>
			  <br>
			  <div class="pagination">
                                <?php echo $links; ?> 
                                </div> 

                                <br>

                                <div class="col-md-12 fleft">
				<div class="form-group" style="width:150px;">
					<label for="action" style="color:#666">Bulk Action</label>
					<select name="action" id="action" class="form-control input-xs" onChange="tableSubmit();">
					<option>--Select--</option>
					<option value="active">Active</option>
					<option value="inactive">Inactive</option>
					<option value="delete">Delete</option>
					</select>
				  </div>
				</div> 
			 
            </div>
			  
		
		