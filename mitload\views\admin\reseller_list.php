<div class="mypage">
<?php if($level!="all") { ?>
			<div class="btn-group">
				 <a href="<?= base_url(); ?>admin/addreseller/<?= $level ?>" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-plus-sign"></span> <?= translate('add_reseller'); ?></a>
			</div>
<?php } ?>
			<div class="top10">&nbsp;</div>

		  <?php echo form_open('' , array('id' => '' , 'class' => 'form-inline filter'));?>
	
				<div class="form-group" style="margin-right:20px;">
			<label for="status"><?= translate('show'); ?></label><br/>
				<select class="form-control input-xs" name="limit" id="myreseller">
		<option value="10"  <?php if($limit=="10") { echo "selected='selected'";}?> >25</option>
				<option value="50"  <?php if($limit=="50") { echo "selected='selected'";}?> >50</option>
				<option value="100"  <?php if($limit=="100") { echo "selected='selected'";}?>>100</option>
				<option value="300" <?php if($limit=="300") { echo "selected='selected'";}?>>300</option>
				<option value="500" <?php if($limit=="500") { echo "selected='selected'";}?>>500</option>
				<option value="1000" <?php if($limit=="1000") { echo "selected='selected'";}?>>1000</option>
				
				</select>
			</div>

		
			<div class="form-group">
			<label for="username"><?= translate('username'); ?></label><br/>
			<input type="text" class="form-control input-xs" name="cname" id="cname" placeholder="<?= translate('username'); ?>" size="20" value="<?php echo $cname;?>">
			</div>

			<div class="form-group">
			<label for="status"><?= translate('reseller'); ?></label><br/>
				<select class="form-control input-xs" name="resel" id="resel">
				<option value="all" <?php if($resel=="all") { echo "selected='selected'";}?>>--<?= translate('all'); ?>--</option>
				<option value="<?= $uid ?>" <?php if($resel=="1") { echo "selected='selected'";}?> ><?= translate('my_reseller'); ?></option>
				
				</select>
			</div>
			

			<div class="form-group">
			<label for="status"><?= translate('status'); ?></label><br/>
				<select class="form-control input-xs" name="status" id="status">
				<option value="all" <?php if($status=="all") { echo "selected='selected'";}?>>--Any--</option>
				<option value="1" <?php if($status=="1") { echo "selected='selected'";}?> ><?= translate('active'); ?></option>
				<option value="0" <?php if($status=="0") { echo "selected='selected'";}?> ><?= translate('inactive'); ?></option>
				</select>
			</div>
			
		
						
			<div class="form-group">
			<label class="" for="exampleInputPassword2">&nbsp;</label><br/>
			
			<button type="submit" class="btn btn-danger btn-xs"><span class="glyphicon glyphicon-search"></span><?= translate('filter'); ?></button>
			<input type="hidden" class="btn btn-primary" name="todo" value="Search">
			</div>
			</form>			<div class="top10">&nbsp;</div>

		
				
				 <?php echo form_open('' , array('name' => 'dataform' , 'role' => 'form'));?>
		<div class="table-responsive">
		<table class="table table-striped table-hover">
				<thead>
					<tr>
						<th><input type="checkbox" name="checkAll" onclick="checkallbox(this);"></th>
 <th><?php echo translate('id'); ?> </th>
                    <th><?php echo translate('username'); ?> </th>
                     <th><?php echo translate('name'); ?> </th>
                    
                    <th><?php echo translate('password'); ?> </th>
                   
                    <th><?php echo translate('main bal'); ?></th>
                       <th><?php echo translate('bank bal'); ?></th>
                      <th><?php echo translate('drive bal'); ?></th>
                    <th><?php echo translate('stock'); ?></th>
                    <th><?php echo translate('last_login'); ?></th>
					 <th><?php echo translate('otp'); ?></th>
					 <th><?php echo translate('created'); ?></th>
					  <th><?php echo translate('status'); ?></th>
					  <th><?php echo translate('parent'); ?></th>
                    <th class="text-nowrap"><?= translate('action'); ?></th>
                  </tr>
                </thead>
                <tbody>
				<?php
				// Initialize totals
				$subamount = 0;
				$stock = 0;
				$bl = 0;
				$bls = 0;

				foreach ($reseller_all as $row)
					{
						 $sts=$row->status; 
						 $otpsts=$row->enbale_otp; 
						 $p_id=$row->p_id;
                        
                         
                         $m_id=$row->id;
                         $cuslevel=$row->custype;
				 
$subamount+=$bl; 
	   $stock+=$bls;
	   
$sql3="SELECT * FROM `reseller` WHERE id='$p_id'"; 
$queryresl = $this->db->query($sql3);
foreach ($queryresl->result() as $row3){

$parentuser=$row3->username; 
}
							



if($cuslevel=="reseller1") {$clave="L1";}
if($cuslevel=="reseller2") {$clave="L2";}
if($cuslevel=="reseller3") {$clave="L3";}
if($cuslevel=="reseller4") {$clave="L4";}
if($cuslevel=="reseller5") {$clave="L5";}
if($cuslevel=="subadmin") {$clave="admin";}
 
							?>

                  <tr>
				  <td><input type="checkbox" name="id[]" value="<?php echo $row->id;  ?>" ></td>

                    <td><?php echo $row->id;  ?></td><td><a href="<?= base_url(); ?>admin/reseller_edit/<?php echo $this->mdb->passwordChanger('encrypt', $m_id); ?>"><?php echo $row->username;  ?> (<font color="black"><?= $clave ?></font>)</a></td>
  <td><?php echo $row->name;  ?></td>
                    <td><a href='javascript:void(0)' onclick="javascript:PopUp('<?= base_url(); ?>admin/changeSecurelogin/<?php echo $this->mdb->passwordChanger('encrypt', $m_id); ?>', 590, 500); return false;" title="Change" class="btn btn-info btn-xs"><?= translate('change'); ?></a></td>

                    <td><?php echo $row->balance;  ?></td>
                     <td><?php echo $row->bank_balance;  ?></td>
                      <td><?php echo $row->drive_bal;  ?></td>
 <td><?php echo $row->balance_limit;  ?></td>
                    <td><?php echo $row->last_login;  ?></td>
					  <td><?php  if($otpsts==1){  ?>
					  <img src="<?= base_url(); ?>assets/img/active.png">
					  <?php }else { ?>
					   <img src="<?= base_url(); ?>assets/img/cross.png">
					  <?php } ?>
					  </td>
					 <td><?php echo $row->create_date;  ?></td>
					  <td><?php  if($sts==1){  ?>
					  <img src="<?= base_url(); ?>assets/img/active.png">
					  <?php }else { ?>
					   <img src="<?= base_url(); ?>assets/img/cross.png">
					  <?php } ?>
					  </td>
					  <td><?php echo $parentuser;  ?></td>
					   
					  
                    <td class="text-nowrap">
                    <a href='javascript:void(0)' onclick="javascript:PopUp('<?= base_url(); ?>admin/adminPayment/<?php echo $this->mdb->passwordChanger('encrypt', $m_id); ?>', 590, 500); return false;" title="Add Balance" class="btn btn-success btn-xs"><?= translate('add_balance'); ?></a>


					| 
						
						<a href="<?= base_url(); ?>admin/payments/<?php echo $this->mdb->passwordChanger('encrypt', $m_id); ?>" title="Payment History"><?= translate('history'); ?></a> |
						
					
					<a href="<?= base_url(); ?>admin/rate/<?php echo $this->mdb->passwordChanger('encrypt', $m_id); ?>"><?= translate('rates'); ?></a> |

						<a href="<?= base_url(); ?>admin/tsend/<?php echo $this->mdb->passwordChanger('encrypt', $m_id); ?>"><?= translate('Msg'); ?></a> |


					<a href="<?= base_url(); ?>admin/reseller_edit/<?php echo $this->mdb->passwordChanger('encrypt', $m_id); ?>"><?= translate('edit'); ?></a>
					
| <a href="<?= base_url(); ?>admin/resellers/<?php echo "9.$m_id"; ?>"><?= translate('List'); ?></a>



						</td>
                  </tr>
                 
					<?php } ?>
				 
                </tbody>
              </table>
			  </div>
			  <br>
			  <br>
			  
			  <?php 
			  
			  // Initialize additional totals
  $subbankamount = 0;
  $subdriveamount = 0;

  foreach ($reseller_bl as $rows)
					{




$subamount+=$rows->balance;
	   $stock+=$rows->balance_limit;;
	   $subbankamount+=$rows->bank_balance;
                   $subdriveamount+=$rows->drive_bal;
					}	  
			  echo"Total main: $subamount bank : $subbankamount Drive: $subdriveamount Stock: $stock";  ?>
			  <div class="pagination">
                                <?php echo $links; ?> 
                                </div> 
								<br>
								
								
				
								<div class="col-md-12 fleft">
				<div class="form-group" style="width:150px;">
					<label for="action" style="color:#666"><?= translate('bulk_action'); ?></label>
					<select name="action" id="action" class="form-control input-xs" onChange="tableSubmit();">
					<option>--<?= translate('select'); ?>--</option>
					<option value="active"><?= translate('active'); ?></option>
					<option value="inactive"><?= translate('inactive'); ?></option>
					<option value="otp"><?= translate('Cancel otp'); ?></option>
					<option value="delete"><?= translate('delete'); ?></option>
					</select>
				  </div>
				</div> 
			 
            </div>
			  
		
		
      <!-- /.row -->
