<div class="mypage">	
<?php echo validation_errors(); ?>

				  <div class="table-responsive">
                  
                  <?php echo form_open('' , array('id' => 'authentication', 'class' => 'inform well', 'style' => 'width:650px;'));?>
			
			<!--<form action="" role="form" class="inform well" style="width:650px;" method="post" accept-charset="utf-8">-->
			
			<div style="display:none">
			<input type="hidden" name="p_id" value="<?php echo $uid; ?>">

</div>			  <table style="width:100%;">
			  <tr>
			  <td style="width:50%;vertical-align:top;padding-right:15px;">
			  <p class="help-block">Login Information</p>
			   <?php 
                        if(form_error('username')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
				<label class="control-label" for="username">Username</label>
				<input type="text" name="username" id="username" class="form-control input-sm" placeholder="Username" value="<?=set_value('username')?>" required>
					<p class="help-block form_error"><?php echo form_error(); ?></p>
			  </div>
			    <?php 
                        if(form_error('password')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
				<label class="control-label" for="password">Password</label>
				<input type="password" name="password" id="password" class="form-control input-sm" placeholder="Password" value="" required >
				<p class="help-block form_error"><?php echo form_error(); ?></p>
			  </div>
			  
			    <?php 
                        if(form_error('pin')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
				<label class="control-label" for="pin">PIN</label>
				<input type="text" name="pin" id="" class="form-control input-sm" placeholder="Pin Number" value="<?=set_value('pin')?>" onkeypress="return onlyNumbers();">
				<p class="help-block form_error"><?php echo form_error(); ?></p>
			  </div>
			  
			
			  <div class="form-group ">
				<label class="control-label" for="mobile">Mobile Number</label>
				<input type="text" name="mobile" id="mobile" class="form-control input-sm" placeholder="Mobile Number" value="<?=set_value('mobile')?>">
				
			  </div>
			   <div class="form-group ">
				<label class="control-label" for="email">Email</label>
				<input type="text" name="email" id="email" class="form-control input-sm" placeholder="Email" value="" >
				
			  </div>
			  
			   <?php 
                        if(form_error('pincode')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
				<label class="control-label" for="mobile">Your Salf PIN</label>
				<input type="password" name="pincode" id="pincode" class="form-control input-sm" placeholder="Salf PIN" value="">
				<p class="help-block form_error"></p>
			  </div>
			  
			 
			  			 
			  			  </td>
			  <td style="width:50%;vertical-align:top;padding-left:15px;">
			  <p class="help-block" style="padding:0px;"><input type="checkbox" name="checkAll" id="allper" onclick="checkallper(this);"> <label style="cursor:pointer;" for="allper">Reseller Permission</label> </p>
			  <div class="form-group">
			    <?php
								
$sql="SELECT * from module where status=1 order by sorder asc"; 

$query = $this->db->query($sql);

		foreach ($query->result() as $row3)
					{
					   
  

   	$portid=$row3->id;
	$titleop=$row3->title;
	$serviceidper=$row3->serviceid;
	$shortid = isset($row3->order) ? $row3->order : (isset($row3->sorder) ? $row3->sorder : 0);

    
?>


 	<div class="checkbox"><label><input type="checkbox" name="per[]" value="<?php echo $serviceidper;?>"><?php echo $titleop;?></label></div>

                <?php }  ?>
				
				
			

  <p class="help-block">Reseller Access Options</p>
				  
				   <div class="form-group ">
				<div class="checkbox"><label><input type="checkbox" name="webaccess" value="1"  checked > Web </label></div>
				
				<div class="checkbox"><label><input type="checkbox" name="appsaccess" value="1"  checked > Apps</label></div>
				  
				  	<div class="checkbox"><label><input type="checkbox" name="oversale" value="1"checked   > Allow Oversale</label></div>
				
				<div class="checkbox"><label><input type="checkbox" name="apiset" value="1"  checked > Allow Api</label></div>
				
				<div class="checkbox"><label><input type="checkbox" name="active" value="1"  checked > Active</label></div>
		

			

								
			  </div>
								
			  </div>
			  
			
			  
			 
			 
			  </td>
			  </tr>
			  </table>
			  <p class="help-block line">&nbsp;</p>
			  <input class="button" type="hidden" name="todo" type="submit" value="AddCustomer">
			  <button type="submit" class="btn btn-primary btn-sm">Add Reseller</button>
			</form>			</div> 
            
            	</div> 
                	</div> 