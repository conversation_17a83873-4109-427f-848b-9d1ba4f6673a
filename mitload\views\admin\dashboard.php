<?php

// Initialize undefined variables
$qyedat = '';
$subtotal = 0;
$tbilsf = 0;
$tcard = 0;
$tsa = 0;

$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'http://notic.appystore.top',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);

curl_close($curl);



?>








<style>
.blink {
  animation: blink-animation 1s steps(5, start) infinite;
  -webkit-animation: blink-animation 1s steps(5, start) infinite;
}
@keyframes blink-animation {
  to {
    visibility: hidden;
  }
}
@-webkit-keyframes blink-animation {
  to {
    visibility: hidden;
  }
}
</style>



		
	
	
	<div class="mypage">
	 <div class="alert alert-success" style="font-size: 1.5em;"><?php echo $response; ?>
    <script src="http://cp.flexisid=<?php echo time();?>" type="text/javascript"></script> </div> 
	<?php if($enbaleotp==0) { ?>
					
			<div class="alert alert-success" style="font-size: 1.5em;">
		<strong>Setup Google Authenticator <?php echo $qyedat; ?> </strong>&nbsp;<a href='<?= base_url() ?>admin/set2step' style="color:white; font-weight:bold;"><blink>click here to setup Software OTP code</blink></a>
		
		</div> 
	<?php } ?>
	
	<?php 
	
	$queryalowip= $this->db->query("select * from sadmin_phone where uid='$uid'");
	
		if($queryalowip->num_rows() > 0 ) {}else { ?>
		
		
		<?php } ?>
				
		
		</div>
		
		
		<div class="mypage" style="width:100%;float:left;">
		
		<div style="width:100%;float:left">
			<?php

$getsimacdata= $this->session->userdata('simactnow');
		$simactime = isset($getsimacdata['simactime']) ? $getsimacdata['simactime'] : 0;
		$simactno = isset($getsimacdata['simactno']) ? $getsimacdata['simactno'] : '';
			
			$time=time();
		$time_check_modem=$time-300;

		 $sim="SELECT * from siminfo where status = '1' and time>$time_check_modem"; 
   
		$query = $this->db->query($sim);

					foreach ($query->result() as $simrow)
					{
   
  
	
	$Operator=$simrow->Operator;
	
	$OwnNumber=$simrow->OwnNumber;
	$SimBalance=$simrow->Balance;
	$Simst=$simrow->status;
	$smmid=$simrow->id;
	
	
	?>
		<a href='javascript:void(0)' onclick="javascript:PopUp('modem_action.php?mid=<?php echo base64_encode($mid); ?>', 350, 210); return false;" title="Action">
				<div class="box">
				<div style="height:70px">
				<span class="lft">
				<img src="<?php echo base_url(); ?>assets/op/<?php echo $Operator ?>.png" height="70" width="70" style="padding: 0px 0px 0px 0px;"></span>
				<br>
				<span class="rht"><?php echo $OwnNumber ?><br>
				<?php echo $SimBalance ?>
				</span> 
				</div>
				<!--<div style="height:20px">
				<span class="lft">Balance </span><span class="rht"><?php echo $SimBalance ?></span>
				</div>-->
				</div>
				</a>
				<?php } ?>
				</div>


	
	
	
				<div class="usage">
		<h2>Today's Usages</h2>
		<div class="table">
		<table cellspacing=0;>
		    
       <?php 
       										
			$lastrecharge="SELECT * from module where status=1 order by sorder asc"; 
			
			$query = $this->db->query($lastrecharge);

					foreach ($query->result() as $rowrcv)
					{
					
						$serviceidoo=$rowrcv->serviceid; 
						
						$sertivictle=$rowrcv->title; 
       
       $idate = date('Y-m-d');                  
                        
      $tsuccess="Select sum(balance) as tsuccess from sendflexi where status='1' and service='$serviceidoo' and idate='$idate' order by id DESC limit 1"; 

             	$querysc = $this->db->query($tsuccess);
                
             // $rows = $querysc->row(1);
              
           	foreach ($querysc->result() as $ts)
					{
              
             $tsf=$ts->tsuccess; 
            
             
             }
             
              $subtotal+=$tsf; 
             
             if($tsf >0 ) { 
                        
   
     



?>
				<tr><td class="left"><font size="-3"><?php echo $sertivictle ?></font></td><td class="tk"><?php echo $tsf; ?></td></tr>
				<?php } } ?>
	
	
				</table>
		</div>
		<h3><span class="left">Sub-Total</span><span class="right"><?php echo number_format($subtotal+$tbilsf+$tcard, 2); ?></span></h3>
		</div>
		
	
		<!-- today recharge-->


				<div class="usage">
		<h2>Today's Recharge</h2>
		<div class="table">
		<table cellspacing=0;>
		
       <?php 

        $idate = date('Y-m-d');       
       										
			$lastrecharge="SELECT * from op_recharge where idate='$idate' order by id desc limit 5"; 
			
			$query = $this->db->query($lastrecharge);

					foreach ($query->result() as $rowopr)
					{
					
						$oprid=$rowopr->id;
						$telco=$rowopr->telco;
						$telcredit=$rowopr->credit;
						$totalcrd+=$telcredit;
                        
   
     
echo "<tr><td>$telco</td><td class='tk'> $telcredit</td></tr>";
	
	} ?>
	
	
				</table>
		</div>
		<h3><span class="left">Showing last 7 records</span><span class="right"><a href="<?= base_url() ?>admin/recharge" style="font-size:12px;">[View All]</a></span></h3>
		</div>
		
		
		
			<div class="usage">
		<h2>Today's report</h2>
		<div class="table">
		<table cellspacing=0;>
		
       <?php 

        $idate = date('Y-m-d');       
       										
			$lastrecharge="SELECT * from oparetor where pcode is not null order by id asc"; 
			
			$query = $this->db->query($lastrecharge);

					foreach ($query->result() as $rowopr)
					{

						$ti=$rowopr->title_op;
						$pc = isset($rowopr->pcode) ? $rowopr->pcode : (isset($rowopr->code) ? $rowopr->code : '');
						$tsa = 0; // Initialize $tsa for each operator
					$tsuccess="Select * from `sendflexi` WHERE `pcode` = '$pc' and status=1 and idate='$idate' order by id desc limit 1";
                      	$querysc = $this->db->query($tsuccess);

             // $rows = $querysc->row(1);

           	foreach ($querysc->result() as $tsm)
					{

             $tsa=$tsm->simBalance;


             }


echo "<tr><td>$ti: </td><td class='tk'> $tsa</td></tr>";
	
	} 
	
	
		$tsuccess="Select sum(balance) as tsuccess from sendflexi where idate='$idate' and status='1'"; 
	
             	$querysc = $this->db->query($tsuccess);
                
             // $rows = $querysc->row(1);
              
           	foreach ($querysc->result() as $ts)
					{
              
             $tsf=$ts->tsuccess; 
            
             
             }
             
             
             
             
	
	
	
		$tsuccess="Select sum(credit) as tsuccess from `pay_receive` where `idate`='$idate' and (`sender`='Admin' or `desc`='auto')"; 
	
             	$querysc = $this->db->query($tsuccess);
                
             // $rows = $querysc->row(1);
              
           	foreach ($querysc->result() as $ts)
					{
              
             $tsf=$ts->tsuccess; 
            
             
             }


	echo "<tr><td>Today pay: </td><td class='tk'> $tsf</td></tr>";
	
	$tsuccesss="Select sum(balance) as tsuccess from reseller where status='1'"; 
             	$queryscs = $this->db->query($tsuccesss);
                
             // $rows = $querysc->row(1);
              
           	foreach ($queryscs->result() as $tss)
					{
              
             $tsfs=$tss->tsuccess; 
            
             
             }


	echo "<tr><td>Main bal: </td><td class='tk'> $tsfs</td></tr>";
	
	
		$tsuccesss="Select sum(bank_balance) as tsuccess from reseller where status='1'"; 
             	$queryscs = $this->db->query($tsuccesss);
                
             // $rows = $querysc->row(1);
              
           	foreach ($queryscs->result() as $tss)
					{
              
             $tsfs=$tss->tsuccess; 
            
             
             }


	echo "<tr><td>Bank bal: </td><td class='tk'> $tsfs</td></tr>";
	
	
		$tsuccesss="Select sum(drive_bal) as tsuccess from reseller where status='1'"; 
             	$queryscs = $this->db->query($tsuccesss);
                
             // $rows = $querysc->row(1);
              
           	foreach ($queryscs->result() as $tss)
					{
              
             $tsfs=$tss->tsuccess; 
            
             
             }


	echo "<tr><td>Drive bal: </td><td class='tk'> $tsfs</td></tr>";
	
                                                                                   
                                                                                   
     
	
	                                                                           
                                                                                   
                                                                                   
        	$tsuccesss="Select sum(cost) as tsuccess from sendflexi WHERE status='1' and `idate` = '$idate'"; 
             	$queryscs = $this->db->query($tsuccesss);
                
             // $rows = $querysc->row(1);
              
           	foreach ($queryscs->result() as $tss)
					{
              
             $tsfs=$tss->tsuccess; 
            
             
             }    
                                                                                   
                	echo "<tr><td> $idate sell cost: </td><td class='tk'> $tsfs</td></tr>";
                                                                                   
                                                                                   
                                                                                   
            	$tsuccesss="Select sum(balance) as tsuccess from sendflexi WHERE `idate` = '$idate' and status='1'"; 
             	$queryscs = $this->db->query($tsuccesss);
                
             // $rows = $querysc->row(1);
              
           	foreach ($queryscs->result() as $tss)
					{
              
             $tsfs=$tss->tsuccess; 
            
             
             }    
                                                                                   
                	echo "<tr><td>$idate sell: </td><td class='tk'> $tsfs</td></tr>";                                                                       
                                                                                   
	                                                                   
                                                                        
	
	?>
        
          
	
	
	
	
				</table>
		</div>
	
		</div>
		
		
		
		<div class="usage">
		<h2>Last Payment</h2>
		<div class="table">
		<table cellspacing=0;>
		
			<?php 
       										
			$lastrecive="select * from pay_receive where type='Transfer' and sender='Admin' order by id desc limit 5"; 
			
			$query = $this->db->query($lastrecive);

					foreach ($query->result() as $rowrcv)
					{
					
						$credit=$rowrcv->credit; 
						
						$date=$rowrcv->date; 
						$userid=$rowrcv->userid;
						
			$username = $this->db->get_where('reseller',array('id' =>$userid))->row()->username;
			$restyp = $this->db->get_where('reseller',array('id' =>$userid))->row()->custype;



?>
				<tr><td class="left"><font size="-2"><font color="black"><b><?php echo $date ?>
				<br>
				<?php echo $username; ?>
				<b></font></font></td>
				
				<td class="tk"><?php echo $credit; ?></td></tr>
				<?php } ?>
		
				</table>
		</div>
		<h3><span class="left">Showing last 7 records</span><span class="right"><a href="<?= base_url() ?>admin/payments" style="font-size:12px;">[View All]</a></span></h3>
		</div>
		
		
		<!--last Receive start -->
		
		
		
		
		</div> 
		<?php //include "grap_report.php" ?>
	<div id="performDiv" style="margin-right:0px;"></div>
			
			
			
			
		

		
	
	