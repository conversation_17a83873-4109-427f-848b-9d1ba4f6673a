
    <div class="mypage">
	<?php
	// Initialize undefined variables
	$sts = isset($sts) ? $sts : 'all';
	$country = isset($country) ? $country : '';
	?>

	<a href="<?= base_url() ?>admin/country/add" class="btn btn-primary btn-sm"><span class="glyphicon glyphicon-plus-sign"></span><?= translate('add_country'); ?></a>
                <div class="top10">&nbsp;</div>
       
	   	<?php echo form_open('' , array('role' => 'form', 'class' => 'form-inline'));?>

        <div class="form-group">
            <label for="name">Name</label><br/>
            <input type="text" class="form-control input-xs" name="country" id="country" placeholder="Country Name" size="25" value="<?php if(isset($country)) { echo $country;}?>">
        </div>
        <div class="form-group">
            <label for="status">Status</label><br/>
            <select class="form-control input-xs" name="sts" id="sts">
                <option value="all" <?php if($sts=="all") { echo "selected='selected'";}?>>--Any--</option>
              
                <option value="1" <?php if($sts=="1") { echo "selected='selected'";}?>>Active</option>
                <option value="0" <?php if($sts=="0") { echo "selected='selected'";}?>>Inactive</option>
            </select>
        </div>
        <div class="form-group">
            <label class="" for="">&nbsp;</label><br/>
            <button type="submit" class="btn btn-danger btn-xs"><span class="glyphicon glyphicon-search"></span> Filter</button>
        </div>
        </form>
		
		<div class="top10">&nbsp;</div>
        <table class="table table-striped table-hover">
            <thead>
            <tr>
                                    <th >Country Name</th>
                                    <th >Code</th>
                                    <th >Currency</th>
                                    <th >Phone</th>
                                    <th >Status</th>
                            </tr>
            </thead>
            <tbody>
			
			<?php 
             

			 
             
            if($country!="" && $sts=="all") { 
            $sql = "SELECT * FROM `country` where `country_name` LIKE '%$country%' order by id desc"; 
             
            }else if($country!="" && $sts!="all") { 
            $sql = "SELECT * FROM `country` where `country_name` LIKE '%$country%' and status='$sts' order by id desc"; 
             
             
			}else if($country=="" && $sts!="") { 
            $sql = "SELECT * FROM `country` where status='$sts' order by id desc"; 
             
             
            }else { 
            $sql = "SELECT * FROM `country` order by id desc"; 
            } 
                   
				    $query89 = $this->db->query($sql);

					foreach ($query89->result() as $row)
					{
						
                    $status_ss=$row->status; 
                 
                     
                    ?>
                            <tr>
							
                    <td> <a href="?sk=country_edit&id=<?php echo $row->id; ?>"> <?php echo $row->country_name; ?></td>
					
                    <td><?php echo $row->code; ?></td>
                    <td><?php echo $row->currency; ?></td>
                    <td><?php echo $row->phone; ?></td>
                    <td>
					<?php if($status_ss==1) { ?>
					<i style="color:green" class="glyphicon glyphicon-ok"></i>
					<?php }else { ?>
					<i class="glyphicon glyphicon-remove" style="color:red;"></i>
					<?php } ?>
					</td>
					<td>

					</td>
                </tr>
				
				<?php } ?>
                           
                        </tbody>
        </table>
    </div>
	
	